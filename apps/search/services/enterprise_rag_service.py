"""
Enterprise RAG Service with advanced LlamaIndex integration.

This module implements an enhanced RAG service using LlamaIndex's enterprise query engines:
- Router query engine for intelligent query routing
- Citation query engine for automatic citation
- Multi-step query engine for complex reasoning
- Specialized query engines for different data types
"""

import logging
import time
import uuid
from typing import Any, Dict, List, Optional, Tuple, Union

from django.conf import settings
from django.contrib.auth.models import User
from django.utils import timezone

from llama_index.core.query_engine import (
    RouterQueryEngine,
    RetrieverQueryEngine,
    CitationQueryEngine,
    MultiStepQueryEngine
)
from llama_index.core.tools import QueryEngineTool
from llama_index.core.selectors import LLMSingleSelector
from llama_index.core.response.schema import Response
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.postprocessor import SimilarityPostprocessor
from llama_index.core.indices import VectorStoreIndex
from llama_index.core.schema import Document, NodeWithScore

from apps.accounts.models import Tenant
from apps.core.utils.collection_manager import get_collection_name
from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
from apps.core.utils.llama_index_llm import get_llm_for_content
from apps.core.utils.llama_index_setup import llama_index_registry
from apps.core.utils.llama_index_vectorstore import get_vector_store
from apps.core.utils.query_intent import classify_query_intent
from apps.core.utils.response_formatter import format_response, humanize_response
from apps.documents.models import DocumentChunk, RawDocument
from apps.search.models import ResultCitation, SearchQuery, SearchResult

logger = logging.getLogger(__name__)


class EnterpriseRAGService:
    """
    Enterprise RAG Service with advanced LlamaIndex integration.

    This service provides:
    - Intelligent query routing
    - Automatic citation
    - Multi-step reasoning
    - Specialized query engines for different data types
    """

    def __init__(self, user: User, tenant_slug: Optional[str] = None):
        """
        Initialize the Enterprise RAG Service.

        Args:
            user: The user making the request
            tenant_slug: The tenant slug to use
        """
        self.user = user
        self.tenant_slug = tenant_slug

        # Get tenant from user profile if not provided
        if not self.tenant_slug and hasattr(user, "profile") and user.profile.tenant:
            self.tenant_slug = user.profile.tenant.slug
            self.tenant = user.profile.tenant
        else:
            # Get tenant from slug
            try:
                self.tenant = Tenant.objects.get(slug=self.tenant_slug)
            except Tenant.DoesNotExist:
                logger.error(f"Tenant with slug {self.tenant_slug} not found")
                self.tenant = None

        # Initialize query engines
        self.query_engines = self._build_query_engines()
        self.router_engine = self._build_router_engine()

    def _build_query_engines(self) -> Dict[str, QueryEngineTool]:
        """
        Build specialized query engines for different data types.

        Returns:
            Dict[str, QueryEngineTool]: Dictionary of query engine tools
        """
        return {
            "slack_conversations": QueryEngineTool.from_defaults(
                query_engine=self._build_conversation_engine(),
                name="slack_conversations",
                description="Search Slack conversations and threads"
            ),
            "github_code": QueryEngineTool.from_defaults(
                query_engine=self._build_code_engine(),
                name="github_code",
                description="Search GitHub repositories, PRs, and issues"
            ),
            "documents": QueryEngineTool.from_defaults(
                query_engine=self._build_document_engine(),
                name="documents",
                description="Search general documents and files"
            )
        }

    def _build_router_engine(self) -> RouterQueryEngine:
        """
        Build router engine for intelligent query routing.

        Returns:
            RouterQueryEngine: Router query engine
        """
        return RouterQueryEngine(
            selector=LLMSingleSelector.from_defaults(llm=get_llm_for_content()),
            query_engine_tools=list(self.query_engines.values()),
            verbose=True
        )

    def _build_conversation_engine(self) -> RetrieverQueryEngine:
        """
        Build specialized query engine for conversations.

        Returns:
            RetrieverQueryEngine: Query engine for conversations
        """
        # Get vector store for conversations
        collection_name = get_collection_name(self.tenant_slug, intent="conversation")
        vector_store = get_vector_store(collection_name=collection_name)

        # Create index
        index = VectorStoreIndex.from_vector_store(vector_store)

        # Create retriever with conversation-specific settings
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=10,
            vector_store_kwargs={"filter": {"source_type": "slack"}}
        )

        # Create query engine
        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            node_postprocessors=[
                SimilarityPostprocessor(similarity_cutoff=0.7)
            ]
        )

    def _build_code_engine(self) -> RetrieverQueryEngine:
        """
        Build specialized query engine for code.

        Returns:
            RetrieverQueryEngine: Query engine for code
        """
        # Get vector store for code
        collection_name = get_collection_name(self.tenant_slug, intent="code")
        vector_store = get_vector_store(collection_name=collection_name)

        # Create index
        index = VectorStoreIndex.from_vector_store(vector_store)

        # Create retriever with code-specific settings
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=15,
            vector_store_kwargs={"filter": {"source_type": "github"}}
        )

        # Create query engine
        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            node_postprocessors=[
                SimilarityPostprocessor(similarity_cutoff=0.6)
            ]
        )

    def _build_document_engine(self) -> RetrieverQueryEngine:
        """
        Build specialized query engine for documents.

        Returns:
            RetrieverQueryEngine: Query engine for documents
        """
        # Get vector store for documents
        collection_name = get_collection_name(self.tenant_slug, intent="document")
        vector_store = get_vector_store(collection_name=collection_name)

        # Create index
        index = VectorStoreIndex.from_vector_store(vector_store)

        # Create retriever with document-specific settings
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=10
        )

        # Create query engine
        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            node_postprocessors=[
                SimilarityPostprocessor(similarity_cutoff=0.7)
            ]
        )

    def search(
        self,
        query_text: str,
        top_k: int = 20,
        metadata_filter: Optional[Dict[str, Any]] = None,
        use_hybrid_search: bool = True,
        use_context_aware: bool = True,
        use_query_expansion: bool = False,
        use_multi_step_reasoning: bool = False,
        output_format: str = "text",
        min_relevance_score: float = 0.4,
    ) -> Tuple[SearchResult, List[Tuple[Document, float]]]:
        """
        Execute query with advanced routing and citation.

        Args:
            query_text: The query text
            top_k: Number of results to return
            metadata_filter: Filter to apply to search results
            use_hybrid_search: Whether to use hybrid search
            use_context_aware: Whether to use context-aware retrieval
            use_query_expansion: Whether to use query expansion
            use_multi_step_reasoning: Whether to use multi-step reasoning
            output_format: Output format for the response
            min_relevance_score: Minimum relevance score for documents

        Returns:
            Tuple[SearchResult, List[Tuple[Document, float]]]: Search result and retrieved documents
        """
        # Create search query in database
        search_query = SearchQuery.objects.create(
            user=self.user,
            tenant=self.tenant,
            query_text=query_text,
            search_params={
                "top_k": top_k,
                "use_hybrid_search": use_hybrid_search,
                "use_context_aware": use_context_aware,
                "use_query_expansion": use_query_expansion,
                "use_multi_step_reasoning": use_multi_step_reasoning,
                "output_format": output_format,
                "min_relevance_score": min_relevance_score,
            },
        )

        try:
            # Determine query intent for specialized collections
            query_intent = classify_query_intent(query_text)
            logger.info(f"Query intent: {query_intent}")

            # Start timer for processing time measurement
            start_time = time.time()

            # Use CitationQueryEngine for automatic citation
            citation_engine = CitationQueryEngine.from_args(
                self.router_engine,
                citation_chunk_size=512,
                citation_top_k=top_k
            )

            # Execute query
            response = citation_engine.query(query_text)

            # Calculate processing time
            processing_time = time.time() - start_time

            # Extract source nodes for citations
            source_nodes = response.source_nodes

            # Convert source nodes to documents
            retrieved_docs = self._convert_nodes_to_documents(source_nodes)

            # Filter documents by relevance score
            filtered_docs = [
                (doc, score) for doc, score in retrieved_docs
                if score >= min_relevance_score
            ]

            # Calculate average retriever score
            retriever_score_avg = sum([score for _, score in filtered_docs]) / len(filtered_docs) if filtered_docs else 0

            # Format response based on output format
            formatted_answer = format_response(
                response.response,
                output_format=output_format
            )

            # Create search result
            search_result = SearchResult.objects.create(
                search_query=search_query,
                user=self.user,
                generated_answer=formatted_answer,
                retriever_score_avg=retriever_score_avg,
                llm_confidence_score=0.9,  # TODO: Get confidence score from LLM
            )

            # Create citations
            self._create_citations(search_result, filtered_docs)

            # Log search result
            logger.info(
                f"Search completed in {processing_time:.2f}s with {len(filtered_docs)} documents"
            )

            return search_result, filtered_docs

        except Exception as e:
            logger.error(f"Error in search: {str(e)}", exc_info=True)

            # Create error search result
            error_message = f"An error occurred while processing your query: {str(e)}"
            search_result = SearchResult.objects.create(
                search_query=search_query,
                user=self.user,
                generated_answer=error_message,
                retriever_score_avg=0.0,
                llm_confidence_score=0.0,
            )

            return search_result, []

    def _convert_nodes_to_documents(
        self, nodes: List[NodeWithScore]
    ) -> List[Tuple[Document, float]]:
        """
        Convert nodes to documents.

        Args:
            nodes: List of nodes with scores

        Returns:
            List[Tuple[Document, float]]: List of documents with scores
        """
        documents = []

        for node in nodes:
            # Create document from node
            doc = Document(
                text=node.node.text,
                metadata=node.node.metadata
            )

            # Add to documents list
            documents.append((doc, node.score))

        return documents

    def _create_citations(
        self, search_result: SearchResult, documents: List[Tuple[Document, float]]
    ) -> None:
        """
        Create citations for search result with deduplication.

        Args:
            search_result: Search result to create citations for
            documents: List of documents with scores
        """
        seen_chunks = set()  # Track chunks we've already created citations for
        citation_count = 0

        for rank, (doc, score) in enumerate(documents):
            # Get document chunk from metadata
            chunk_id = doc.metadata.get("chunk_id")

            if not chunk_id:
                logger.warning(f"No chunk_id in metadata: {doc.metadata}")
                continue

            try:
                # Get document chunk
                chunk = DocumentChunk.objects.get(id=chunk_id)

                # Check if we've already created a citation for this chunk
                if chunk.id in seen_chunks:
                    logger.debug(f"Skipping duplicate citation for chunk {chunk.id}")
                    continue

                # Check if citation already exists in database (extra safety)
                existing_citation = ResultCitation.objects.filter(
                    result=search_result,
                    document_chunk=chunk
                ).first()

                if existing_citation:
                    logger.debug(f"Citation already exists for result {search_result.id} and chunk {chunk.id}")
                    seen_chunks.add(chunk.id)
                    continue

                # Create citation
                ResultCitation.objects.create(
                    result=search_result,
                    document_chunk=chunk,
                    relevance_score=score,
                    rank=citation_count + 1,  # Use actual citation count for rank
                )
                seen_chunks.add(chunk.id)
                citation_count += 1

            except DocumentChunk.DoesNotExist:
                logger.warning(f"Document chunk with id {chunk_id} not found")
            except Exception as e:
                logger.error(f"Error creating citation: {str(e)}", exc_info=True)

        logger.info(f"Created {citation_count} unique citations for search result {search_result.id}")