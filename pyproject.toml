[tool.poetry]
name = "multi-source-rag"
version = "0.1.0"
description = "Multi-Source RAG System with Django"
authors = ["<PERSON><PERSON><PERSON>"]
readme = "README.md"
packages = []

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
django = "4.2.10"
djangorestframework = "3.14.0"
psycopg2-binary = "2.9.9"
python-dotenv = "1.0.0"
dj-database-url = "2.1.0"
whitenoise = "6.6.0"
gunicorn = "21.2.0"
sentence-transformers = "2.6.1"
huggingface-hub = "0.30.2"
qdrant-client = "^1.7.1"
pgvector = "0.2.4"
django-debug-toolbar = "^5.2.0"
pygithub = "^2.6.1"
slack-sdk = "^3.27.1"
rank-bm25 = "^0.2.2"
pydantic = "^2.6.3"
spacy = "^3.7.2"
scikit-learn = "^1.3.2"  # For clustering in topic-based documents
bleach = "^6.2.0"
selenium = "^4.32.0"
llama-index = "^0.12.37"
llama-index-embeddings-huggingface = "^0.5.4"
llama-index-llms-ollama = "^0.5.4"
llama-index-vector-stores-qdrant = "^0.6.0"
fastembed = "^0.7.0"
llama-index-core = "^0.12.37"
llama-index-llms-gemini = "^0.4.14"
llama-index-embeddings-gemini = "^0.3.2"


[tool.poetry.group.dev.dependencies]
black = "24.3.0"
isort = "5.13.2"
flake8 = "7.0.0"
pytest = "8.0.0"
pytest-django = "4.8.0"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
