"""
LlamaIndex LLM integration.

This module provides integration with LlamaIndex LLMs, including:
- Ollama LLM integration
- LLM management
- Response generation utilities
"""

import logging
from typing import Any, Dict, List, Optional, Union

from django.conf import settings
from llama_index.llms.ollama import Ollama

from apps.core.utils.llama_index_setup import llama_index_registry
from apps.core.utils.prompt_templates import get_prompt_template

logger = logging.getLogger(__name__)

# Ollama API endpoint
OLLAMA_API_HOST = getattr(settings, "OLLAMA_API_HOST", "http://localhost:11434")
OLLAMA_MODEL_NAME = getattr(settings, "OLLAMA_MODEL_NAME", "llama3")


def initialize_llms() -> None:
    """Initialize and register LLMs with the registry."""
    # Initialize Gemini LLM first (primary)
    try:
        from apps.core.utils.gemini_llm import initialize_gemini_llms
        initialize_gemini_llms()
    except Exception as e:
        logger.error(f"Failed to initialize Gemini LLM: {str(e)}")

    # Initialize Ollama LLM (fallback)
    try:
        ollama_llm = Ollama(
            model=OLLAMA_MODEL_NAME,
            base_url=OLLAMA_API_HOST,
            request_timeout=120.0,
            temperature=0.1,
            context_window=4096,
            additional_kwargs={
                "top_p": 0.95,
            },
        )
        llama_index_registry.register_llm("ollama", ollama_llm)
        logger.info(f"Initialized Ollama LLM with model: {OLLAMA_MODEL_NAME}")
    except Exception as e:
        logger.error(f"Failed to initialize Ollama LLM: {str(e)}")

    logger.info(f"Initialized LLMs: {list(llama_index_registry._llms.keys())}")


def get_llm(
    model_name: Optional[str] = None,
    temperature: float = 0.1,
    streaming: bool = False,
) -> Any:
    """
    Get an LLM instance (Gemini primary, Ollama fallback).

    Args:
        model_name: Name of the model to use
        temperature: Temperature for generation
        streaming: Whether to enable streaming

    Returns:
        LLM instance (Gemini or Ollama)
    """
    # Try Gemini first
    try:
        from apps.core.utils.gemini_llm import get_gemini_llm, is_gemini_available

        if is_gemini_available():
            return get_gemini_llm(
                model_name=model_name,
                temperature=temperature,
                max_tokens=4096,
            )
    except Exception as e:
        logger.warning(f"Failed to get Gemini LLM, falling back to Ollama: {str(e)}")

    # Fallback to Ollama
    try:
        model_name = model_name or OLLAMA_MODEL_NAME
        ollama_llm = Ollama(
            model=model_name,
            base_url=OLLAMA_API_HOST,
            request_timeout=120.0,
            temperature=temperature,
            context_window=4096,
            additional_kwargs={
                "top_p": 0.95,
            },
            streaming=streaming,
        )
        return ollama_llm
    except Exception as e:
        logger.error(f"Failed to create Ollama LLM with model {model_name}: {str(e)}")
        # Fall back to registry default as last resort
        return llama_index_registry.get_llm()


def generate_response(
    query: str,
    context_docs: List[Dict[str, Any]],
    query_type: Optional[str] = None,
    additional_instructions: str = "",
    temperature: float = 0.1,
    streaming: bool = False,
) -> str:
    """
    Generate a response using the LLM.

    Args:
        query: Query to answer
        context_docs: Context documents
        query_type: Type of query
        additional_instructions: Additional instructions for the LLM
        temperature: Temperature for generation
        streaming: Whether to enable streaming

    Returns:
        str: Generated response
    """
    # Get LLM
    llm = get_llm(temperature=temperature, streaming=streaming)

    # Format context
    formatted_context = format_context_docs(context_docs)

    # Get prompt template
    prompt_template = get_prompt_template(
        query=query,
        query_type=query_type,
        additional_instructions=additional_instructions,
    )

    # Format prompt
    formatted_prompt = prompt_template.format(context=formatted_context, question=query)

    # Generate response
    try:
        response = llm.complete(formatted_prompt)
        return response.text
    except Exception as e:
        logger.error(f"Error generating response: {str(e)}")
        return f"I encountered an error while generating a response: {str(e)}"


def format_context_docs(context_docs: List[Dict[str, Any]]) -> str:
    """
    Format context documents for the LLM.

    Args:
        context_docs: Context documents

    Returns:
        str: Formatted context
    """
    formatted_docs = []

    for i, doc in enumerate(context_docs):
        # Extract content
        content = doc.get("content", "")
        if not content and "page_content" in doc:
            content = doc.get("page_content", "")

        # Extract metadata
        metadata = doc.get("metadata", {})
        metadata_str = ""

        # Format metadata
        if metadata:
            relevant_fields = [
                "title",
                "source_type",
                "author",
                "created_at",
                "url",
                "chunk_id",
                "document_id",
            ]
            metadata_items = []

            for field in relevant_fields:
                if field in metadata and metadata[field]:
                    metadata_items.append(
                        f"{field.replace('_', ' ').title()}: {metadata[field]}"
                    )

            if metadata_items:
                metadata_str = f"\n[{', '.join(metadata_items)}]"

        # Format document
        formatted_docs.append(f"Document {i+1}:\n{content}{metadata_str}")

    # Join documents
    return "\n\n".join(formatted_docs)
