"""
LlamaIndex embedding models integration.

This module provides integration with LlamaIndex embedding models, including:
- HuggingFace embedding models
- Domain-specific embedding models
- Embedding model management
"""

import logging
from typing import Any, Dict, List, Optional, Union

from django.conf import settings
from llama_index.embeddings.huggingface import HuggingFaceEmbedding

from apps.accounts.models import Tenant
from apps.core.models import EmbeddingModel
from apps.core.utils.domain_embeddings import domain_embeddings_manager
from apps.core.utils.llama_index_setup import llama_index_registry

logger = logging.getLogger(__name__)


def initialize_embedding_models() -> None:
    """Initialize and register embedding models with the registry."""
    # Initialize Gemini embedding first (primary)
    try:
        from apps.core.utils.gemini_llm import initialize_gemini_embeddings
        initialize_gemini_embeddings()
    except Exception as e:
        logger.error(f"Failed to initialize Gemini embeddings: {str(e)}")

    # Initialize default HuggingFace embedding model (fallback)
    default_model_name = getattr(
        settings, "EMBEDDING_MODEL_NAME", "sentence-transformers/all-MiniLM-L6-v2"
    )

    # Create and register default embedding model
    default_embedding = HuggingFaceEmbedding(
        model_name=default_model_name,
        embed_batch_size=32,
    )
    llama_index_registry.register_embedding("huggingface", default_embedding)

    # Register domain-specific embedding models if available
    for domain, model_info in domain_embeddings_manager.get_available_models().items():
        model_name = model_info.get("model_name")
        if model_name:
            try:
                domain_embedding = HuggingFaceEmbedding(
                    model_name=model_name,
                    embed_batch_size=32,
                )
                llama_index_registry.register_embedding(f"domain_{domain}", domain_embedding)
                logger.info(f"Registered domain-specific embedding model for {domain}: {model_name}")
            except Exception as e:
                logger.error(f"Failed to initialize domain embedding model for {domain}: {str(e)}")

    logger.info(f"Initialized embedding models: {list(llama_index_registry._embeddings.keys())}")


def get_embedding_model_for_content(
    content_type: Optional[str] = None,
    domain: Optional[str] = None,
    tenant_slug: Optional[str] = None,
    model_name: Optional[str] = None,
) -> Any:
    """
    Get the appropriate embedding model based on content type, domain, or tenant.
    Uses Gemini as primary, HuggingFace as fallback.

    Args:
        content_type: Type of content to embed
        domain: Domain of content to embed
        tenant_slug: Tenant slug for tenant-specific models
        model_name: Specific model name to use (overrides other parameters)

    Returns:
        Embedding model instance (Gemini or HuggingFace)
    """
    # Try Gemini embedding first (unless specific model requested)
    if not model_name:
        try:
            from apps.core.utils.gemini_llm import get_gemini_embedding, is_gemini_available

            if is_gemini_available():
                return get_gemini_embedding()
        except Exception as e:
            logger.warning(f"Failed to get Gemini embedding, falling back to HuggingFace: {str(e)}")

    # If model name is provided, use it directly
    if model_name:
        # Check if model is already registered
        if f"custom_{model_name}" in llama_index_registry._embeddings:
            return llama_index_registry.get_embedding(f"custom_{model_name}")

        # Create and register new model
        try:
            custom_embedding = HuggingFaceEmbedding(
                model_name=model_name,
                embed_batch_size=32,
            )
            llama_index_registry.register_embedding(f"custom_{model_name}", custom_embedding)
            return custom_embedding
        except Exception as e:
            logger.error(f"Failed to initialize custom embedding model {model_name}: {str(e)}")
            # Fall back to default model
            return llama_index_registry.get_embedding()

    # Check for domain-specific model
    if domain and f"domain_{domain}" in llama_index_registry._embeddings:
        return llama_index_registry.get_embedding(f"domain_{domain}")

    # Check for content-type specific model
    if content_type and f"content_{content_type}" in llama_index_registry._embeddings:
        return llama_index_registry.get_embedding(f"content_{content_type}")

    # Check for tenant-specific model
    if tenant_slug and f"tenant_{tenant_slug}" in llama_index_registry._embeddings:
        return llama_index_registry.get_embedding(f"tenant_{tenant_slug}")

    # Fall back to default model
    return llama_index_registry.get_embedding()


def get_or_create_embedding_model(
    tenant: Tenant,
    model_name: str = "sentence-transformers/all-MiniLM-L6-v2",
    dimension: int = 384,
) -> EmbeddingModel:
    """
    Get or create an embedding model in the database.

    Args:
        tenant: Tenant to create model for
        model_name: Name of the embedding model
        dimension: Dimension of the embedding model

    Returns:
        EmbeddingModel: Database model for the embedding model
    """
    # Get or create embedding model
    embedding_model, created = EmbeddingModel.objects.get_or_create(
        tenant=tenant,
        model_name=model_name,
        defaults={
            "name": f"Embedding Model ({model_name.split('/')[-1]})",
            "dimension": dimension,
            "is_default": True,
        },
    )

    if created:
        logger.info(f"Created embedding model: {embedding_model.name}")

    return embedding_model


def convert_to_llama_index_embeddings(
    texts: List[str],
    model_name: Optional[str] = None,
) -> List[List[float]]:
    """
    Convert texts to embeddings using LlamaIndex embedding models.

    Args:
        texts: List of texts to embed
        model_name: Name of the embedding model to use

    Returns:
        List[List[float]]: List of embeddings
    """
    # Get embedding model
    embedding_model = get_embedding_model_for_content(model_name=model_name)

    # Generate embeddings
    embeddings = embedding_model.get_text_embedding_batch(texts)

    return embeddings
